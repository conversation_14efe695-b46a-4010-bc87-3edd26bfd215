# -*- coding: utf-8 -*-
import json
from dataclasses import dataclass
from enum import Enum
from typing import Dict, Any, Optional
from tools import utils, host_uuid
import requests

class Environment(Enum):
    DEV = "dev"
    PROD = "prod"


@dataclass
class TaskData:
    id: Optional[int] = None
    userId: Optional[int] = None
    kwUserUuid: Optional[str] = None
    taskId: Optional[str] = None
    kwUuid: Optional[str] = None
    keyword: Optional[str] = None
    status: Optional[int] = None
    platform: Optional[str] = None
    taskContext: Optional[str] = None


class ServiceApi:
    # 环境配置映射
    ENV_URLS = {
        # Environment.DEV: "http://127.0.0.1:9051",
        Environment.DEV: "https://api.jinsuosuo.com/core",
        Environment.PROD: "https://api.jinsuosuo.com/core"
    }



    def __init__(self, env: Environment = Environment.DEV):
        self.sign = "da54dde7886b041210c5eb1b6914b4b3"
        self.env = env
        # 根据环境设置基础URL
        self.base_url = self.ENV_URLS.get(env)
        self.agent_uuid = host_uuid.get_host_uuid()

    def query_task_ack(self) -> dict:
        """
        调用 /backend/task/ack 接口确认任务

        Returns:
            Dict[str, Any]: API响应结果，格式为:
            {
                "code": "success",  # 成功为 "success"，失败为 "error"
                "data": TaskData 对象或列表,
                "msg": "操作成功"  # 提示信息
            }
        """
        try:
            # 获取设备唯一标识
            agent_uuid = host_uuid.get_host_uuid()
            header = {
                "authorization": "96e869447bb4404c844f7e55052e54b8"
            }
            params = {
                "sign": self.sign,
                "agentUuid": self.agent_uuid
            }

            # 构建完整URL
            url = f"{self.base_url}/backend/task/ack"

            # 发送GET请求
            utils.logger.info(f"正在调用任务确认接口: {url}")
            response = requests.get(url, params=params, timeout=50, headers=header)

            # 检查响应状态
            if response.status_code == 200:
                try:
                    result = response.json()
                    utils.logger.info(f"任务确认接口调用成功: {result}")

                    # 处理响应数据
                    if result.get("code") == "success":
                        # 如果有数据，将其转换为 TaskData 对象
                        raw_data = result.get("result")

                        if raw_data:
                            if isinstance(raw_data, dict):
                                return raw_data
                    return None
                except json.JSONDecodeError:
                    utils.logger.error(f"解析响应JSON失败: {response.text}")
                    return None
            else:
                utils.logger.error(f"任务确认接口调用失败，状态码: {response.status_code}, 响应: {response.text}")
                return None

        except requests.RequestException as e:
            utils.logger.error(f"任务确认接口请求异常: {str(e)}")
            return None
        except Exception as e:
            utils.logger.error(f"任务确认接口调用过程中发生未知错误: {str(e)}")
            return None

    def update_task_result(self,
                           task_info: dict[str, Any],
                           task_result: list[dict[str, Any]]
                           ) -> bool:
        try:

            header = {
                "authorization": "96e869447bb4404c844f7e55052e54b8"
            }

            # 构造请求参数
            params = {
                "sign": self.sign,
                "agentUuid": self.agent_uuid,
                "kwTaskAckDTO": dict(task_info),
                "tempDTOList": task_result
            }
            # 构造请求URL
            url = f"{self.base_url}/backend/task/kw/result"
            # 发送POST请求
            response = requests.post(url, json=params, timeout=50, headers=header)
            # 判断响应状态码
            if response.status_code == 200:
                try:
                    # 解析响应JSON
                    result = response.json()
                    # 判断响应结果
                    return result.get("code") == "success"
                except json.JSONDecodeError:
                    # 记录解析响应JSON失败
                    utils.logger.error(f"解析响应JSON失败: {response.text}")
                    return False
        except Exception as e:
            # 记录任务确认接口调用过程中发生未知错误
            utils.logger.error(f"任务确认接口调用过程中发生未知错误: {str(e)}")
            return False


if __name__ == '__main__':
    service_api = ServiceApi(Environment.DEV)
    data = service_api.query_task_ack()
    if not data:
        print("暂无数据")
