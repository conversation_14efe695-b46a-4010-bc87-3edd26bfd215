# -*- coding: utf-8 -*-

import asyncio
import os
import sys
from typing import Any, Dict, Optional, Tuple

from playwright.async_api import (BrowserContext, BrowserType, Page,
                                  async_playwright)

# 添加项目根目录到路径，确保能正确导入模块
project_root = os.path.dirname(os.path.abspath(__file__))
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    import config
    from crawlers.proxy.proxy_ip_pool import IpInfoModel, create_ip_pool
    from crawlers.spiders.douyin.client import DOUYINClient
    from crawlers.spiders.douyin.exception import DataFetchError
    from crawlers.spiders.douyin.field import PublishTimeType, SearchChannelType, SearchSortType
    from crawlers.spiders.douyin.login import DouYinLogin
    from crawlers.browser.virtual_browser_api import VirtualBrowserApi
    from tools import utils
except ImportError as e:
    print(f"导入模块失败: {e}")
    print("请确保在项目根目录下运行此脚本，并且所有依赖项已正确安装")
    sys.exit(1)


class DouyinAPI:
    """抖音API接口类"""

    def __init__(self):
        self.index_url = "https://www.douyin.com"
        self.context_page: Optional[Page] = None
        self.dy_client: Optional[DOUYINClient] = None
        self.browser_context: Optional[BrowserContext] = None
        self.initialized = False
        self.logged_in = False
        # 指纹浏览器相关
        self.virtual_browser_api: Optional[VirtualBrowserApi] = None
        self.virtual_browser_id: Optional[int] = None
        self.use_virtual_browser = False

    async def initialize(self,
                         need_login: bool = False,
                         login_type: str = "qrcode",
                         login_phone: str = "",
                         cookie_str: str = "",
                         headless: bool = True,
                         enable_proxy: bool = False,
                         use_virtual_browser: bool = None) -> Dict[str, Any]:
        """
        初始化抖音API

        Args:
            need_login: 是否需要登录
            login_type: 登录类型 ("qrcode", "phone", "cookie")
            login_phone: 手机号（phone登录时使用）
            cookie_str: Cookie字符串（cookie登录时使用）
            headless: 是否无头模式
            enable_proxy: 是否启用代理
            use_virtual_browser: 是否使用指纹浏览器，None时使用配置文件设置

        Returns:
            Dict: 初始化结果
        """
        try:
            # 确定是否使用指纹浏览器
            if use_virtual_browser is None:
                self.use_virtual_browser = config.ENABLE_VIRTUAL_BROWSER
            else:
                self.use_virtual_browser = use_virtual_browser

            # 设置代理
            playwright_proxy_format, httpx_proxy_format = None, None
            if enable_proxy:
                ip_proxy_pool = await create_ip_pool(config.IP_PROXY_POOL_COUNT, enable_validate_ip=True)
                ip_proxy_info: IpInfoModel = await ip_proxy_pool.get_proxy()
                playwright_proxy_format, httpx_proxy_format = self.format_proxy_info(ip_proxy_info)

            async with async_playwright() as playwright:
                # 启动浏览器
                chromium = playwright.chromium
                self.browser_context = await self.launch_browser(
                    chromium,
                    playwright_proxy_format,
                    user_agent=None,
                    headless=headless
                )

                # 添加反检测脚本
                stealth_js_path = self._get_stealth_js_path()
                await self.browser_context.add_init_script(path=stealth_js_path)
                self.context_page = await self.browser_context.new_page()
                await self.context_page.goto(self.index_url)

                # 创建抖音客户端
                self.dy_client = await self.create_douyin_client(httpx_proxy_format)

                # 检查登录状态
                if need_login:
                    if not await self.dy_client.pong(browser_context=self.browser_context):
                        login_obj = DouYinLogin(
                            login_type=login_type,
                            login_phone=login_phone,
                            browser_context=self.browser_context,
                            context_page=self.context_page,
                            cookie_str=cookie_str
                        )
                        await login_obj.begin()
                        await self.dy_client.update_cookies(browser_context=self.browser_context)
                        self.logged_in = True
                    else:
                        self.logged_in = True
                else:
                    # 不需要登录，直接获取cookie
                    await self.dy_client.update_cookies(browser_context=self.browser_context)

                self.initialized = True

                return {
                    "success": True,
                    "message": "初始化成功",
                    "logged_in": self.logged_in,
                    "data": {
                        "user_agent": await self.context_page.evaluate("() => navigator.userAgent"),
                        "cookies_count": len(await self.browser_context.cookies())
                    }
                }

        except Exception as e:
            utils.logger.error(f"[DouyinAPI.initialize] 初始化失败: {e}")
            return {
                "success": False,
                "message": f"初始化失败: {str(e)}",
                "logged_in": False,
                "data": None
            }

    async def search_videos(self,
                            keyword: str,
                            count: int = 20,
                            sort_type: int = 0,
                            publish_time: int = 0,
                            offset: int = 0) -> Dict[str, Any]:
        """
        搜索关键词获取视频列表

        Args:
            keyword: 搜索关键词
            count: 获取数量，默认20
            sort_type: 排序类型 (0:综合, 1:最多点赞, 2:最新发布)
            publish_time: 发布时间 (0:不限, 1:一天内, 7:一周内, 180:半年内)
            offset: 偏移量，用于分页

        Returns:
            Dict: 搜索结果
        """
        if not self.initialized or not self.dy_client:
            return {"success": False, "message": "请先初始化API", "data": None}

        try:
            utils.logger.info(f"[DouyinAPI.search_videos] 搜索关键词: {keyword}")

            posts_res = await self.dy_client.search_info_by_keyword(
                keyword=keyword,
                offset=offset,
                search_channel=SearchChannelType.VIDEO,
                sort_type=SearchSortType(sort_type),
                publish_time=PublishTimeType(publish_time),
                search_id=""
            )

            if posts_res.get("data") is None:
                return {
                    "success": False,
                    "message": "搜索结果为空",
                    "data": None
                }

            # 提取视频信息
            video_list = []
            for post_item in posts_res.get("data", []):
                try:
                    aweme_info = post_item.get("aweme_info") or \
                                 post_item.get("aweme_mix_info", {}).get("mix_items", [{}])[0]
                    if aweme_info:
                        video_list.append({
                            "aweme_id": aweme_info.get("aweme_id", ""),
                            "desc": aweme_info.get("desc", ""),
                            "create_time": aweme_info.get("create_time", 0),
                            "author": {
                                "uid": aweme_info.get("author", {}).get("uid", ""),
                                "sec_uid": aweme_info.get("author", {}).get("sec_uid", ""),
                                "nickname": aweme_info.get("author", {}).get("nickname", ""),
                                "avatar": aweme_info.get("author", {}).get("avatar_thumb", {}).get("url_list", [""])[0]
                            },
                            "statistics": {
                                "digg_count": aweme_info.get("statistics", {}).get("digg_count", 0),
                                "comment_count": aweme_info.get("statistics", {}).get("comment_count", 0),
                                "share_count": aweme_info.get("statistics", {}).get("share_count", 0),
                                "play_count": aweme_info.get("statistics", {}).get("play_count", 0)
                            },
                            "video_url": aweme_info.get("video", {}).get("play_addr", {}).get("url_list", [""])[0],
                            "cover_url": aweme_info.get("video", {}).get("cover", {}).get("url_list", [""])[0]
                        })
                except (TypeError, KeyError) as e:
                    utils.logger.warning(f"[DouyinAPI.search_videos] 解析视频信息失败: {e}")
                    continue

            return {
                "success": True,
                "message": f"成功获取 {len(video_list)} 个视频",
                "data": {
                    "videos": video_list,
                    "has_more": posts_res.get("has_more", False),
                    "search_id": posts_res.get("extra", {}).get("logid", "")
                }
            }

        except DataFetchError as e:
            utils.logger.error(f"[DouyinAPI.search_videos] 搜索失败: {e}")
            return {
                "success": False,
                "message": f"搜索失败: {str(e)}",
                "data": None
            }

    async def get_video_detail(self, aweme_id: str) -> Dict[str, Any]:
        """
        获取视频详情

        Args:
            aweme_id: 视频ID

        Returns:
            Dict: 视频详情
        """
        if not self.initialized or not self.dy_client:
            return {"success": False, "message": "请先初始化API", "data": None}

        try:
            utils.logger.info(f"[DouyinAPI.get_video_detail] 获取视频详情: {aweme_id}")

            video_detail = await self.dy_client.get_video_by_id(aweme_id)

            if not video_detail:
                return {
                    "success": False,
                    "message": "视频不存在或已被删除",
                    "data": None
                }

            return {
                "success": True,
                "message": "获取视频详情成功",
                "data": video_detail
            }

        except DataFetchError as e:
            utils.logger.error(f"[DouyinAPI.get_video_detail] 获取视频详情失败: {e}")
            return {
                "success": False,
                "message": f"获取视频详情失败: {str(e)}",
                "data": None
            }

    async def get_video_comments(self,
                                 aweme_id: str,
                                 count: int = 20,
                                 cursor: int = 0,
                                 get_sub_comments: bool = False) -> Dict[str, Any]:
        """
        获取视频评论

        Args:
            aweme_id: 视频ID
            count: 获取评论数量，默认20
            cursor: 游标，用于分页
            get_sub_comments: 是否获取子评论

        Returns:
            Dict: 评论列表
        """
        if not self.initialized or not self.dy_client:
            return {"success": False, "message": "请先初始化API", "data": None}

        try:
            utils.logger.info(f"[DouyinAPI.get_video_comments] 获取视频评论: {aweme_id}")

            comments_res = await self.dy_client.get_aweme_comments(aweme_id, cursor)

            if not comments_res.get("comments"):
                return {
                    "success": False,
                    "message": "评论获取失败或无评论",
                    "data": None
                }

            comments_list = []
            for comment in comments_res.get("comments", []):
                comment_info = {
                    "cid": comment.get("cid", ""),
                    "text": comment.get("text", ""),
                    "create_time": comment.get("create_time", 0),
                    "digg_count": comment.get("digg_count", 0),
                    "reply_comment_total": comment.get("reply_comment_total", 0),
                    "user": {
                        "uid": comment.get("user", {}).get("uid", ""),
                        "sec_uid": comment.get("user", {}).get("sec_uid", ""),
                        "nickname": comment.get("user", {}).get("nickname", ""),
                        "avatar": comment.get("user", {}).get("avatar_thumb", {}).get("url_list", [""])[0]
                    },
                    "sub_comments": []
                }

                # 获取子评论
                if get_sub_comments and comment.get("reply_comment_total", 0) > 0:
                    try:
                        sub_comments_res = await self.dy_client.get_sub_comments(comment.get("cid", ""))
                        for sub_comment in sub_comments_res.get("comments", []):
                            comment_info["sub_comments"].append({
                                "cid": sub_comment.get("cid", ""),
                                "text": sub_comment.get("text", ""),
                                "create_time": sub_comment.get("create_time", 0),
                                "digg_count": sub_comment.get("digg_count", 0),
                                "user": {
                                    "uid": sub_comment.get("user", {}).get("uid", ""),
                                    "sec_uid": sub_comment.get("user", {}).get("sec_uid", ""),
                                    "nickname": sub_comment.get("user", {}).get("nickname", ""),
                                    "avatar": sub_comment.get("user", {}).get("avatar_thumb", {}).get("url_list", [""])[
                                        0]
                                }
                            })
                    except Exception as e:
                        utils.logger.warning(f"[DouyinAPI.get_video_comments] 获取子评论失败: {e}")

                comments_list.append(comment_info)

            return {
                "success": True,
                "message": f"成功获取 {len(comments_list)} 条评论",
                "data": {
                    "comments": comments_list,
                    "has_more": comments_res.get("has_more", False),
                    "cursor": comments_res.get("cursor", 0),
                    "total": comments_res.get("total", 0)
                }
            }

        except DataFetchError as e:
            utils.logger.error(f"[DouyinAPI.get_video_comments] 获取评论失败: {e}")
            return {
                "success": False,
                "message": f"获取评论失败: {str(e)}",
                "data": None
            }

    async def get_user_info(self, sec_user_id: str) -> Dict[str, Any]:
        """
        获取用户主页信息

        Args:
            sec_user_id: 用户的sec_user_id

        Returns:
            Dict: 用户信息
        """
        if not self.initialized or not self.dy_client:
            return {"success": False, "message": "请先初始化API", "data": None}

        try:
            utils.logger.info(f"[DouyinAPI.get_user_info] 获取用户信息: {sec_user_id}")

            user_info = await self.dy_client.get_user_info(sec_user_id)

            if not user_info.get("user"):
                return {
                    "success": False,
                    "message": "用户不存在或信息获取失败",
                    "data": None
                }

            user_data = user_info.get("user", {})
            return {
                "success": True,
                "message": "获取用户信息成功",
                "data": {
                    "uid": user_data.get("uid", ""),
                    "sec_uid": user_data.get("sec_uid", ""),
                    "nickname": user_data.get("nickname", ""),
                    "signature": user_data.get("signature", ""),
                    "avatar": user_data.get("avatar_larger", {}).get("url_list", [""])[0],
                    "follower_count": user_data.get("follower_count", 0),
                    "following_count": user_data.get("following_count", 0),
                    "aweme_count": user_data.get("aweme_count", 0),
                    "total_favorited": user_data.get("total_favorited", 0),
                    "verification_type": user_data.get("verification_type", 0),
                    "custom_verify": user_data.get("custom_verify", ""),
                    "enterprise_verify_reason": user_data.get("enterprise_verify_reason", "")
                }
            }

        except DataFetchError as e:
            utils.logger.error(f"[DouyinAPI.get_user_info] 获取用户信息失败: {e}")
            return {
                "success": False,
                "message": f"获取用户信息失败: {str(e)}",
                "data": None
            }

    async def get_user_videos(self,
                              sec_user_id: str,
                              count: int = 20,
                              max_cursor: str = "") -> Dict[str, Any]:
        """
        获取用户发布的作品

        Args:
            sec_user_id: 用户的sec_user_id
            count: 获取数量，默认20
            max_cursor: 游标，用于分页

        Returns:
            Dict: 用户作品列表
        """
        if not self.initialized or not self.dy_client:
            return {"success": False, "message": "请先初始化API", "data": None}

        try:
            utils.logger.info(f"[DouyinAPI.get_user_videos] 获取用户作品: {sec_user_id}")

            videos_res = await self.dy_client.get_user_aweme_posts(sec_user_id, max_cursor)

            if not videos_res.get("aweme_list"):
                return {
                    "success": False,
                    "message": "用户作品获取失败或无作品",
                    "data": None
                }

            videos_list = []
            for aweme in videos_res.get("aweme_list", []):
                videos_list.append({
                    "aweme_id": aweme.get("aweme_id", ""),
                    "desc": aweme.get("desc", ""),
                    "create_time": aweme.get("create_time", 0),
                    "statistics": {
                        "digg_count": aweme.get("statistics", {}).get("digg_count", 0),
                        "comment_count": aweme.get("statistics", {}).get("comment_count", 0),
                        "share_count": aweme.get("statistics", {}).get("share_count", 0),
                        "play_count": aweme.get("statistics", {}).get("play_count", 0)
                    },
                    "video_url": aweme.get("video", {}).get("play_addr", {}).get("url_list", [""])[0],
                    "cover_url": aweme.get("video", {}).get("cover", {}).get("url_list", [""])[0]
                })

            return {
                "success": True,
                "message": f"成功获取 {len(videos_list)} 个作品",
                "data": {
                    "videos": videos_list,
                    "has_more": videos_res.get("has_more", False),
                    "max_cursor": videos_res.get("max_cursor", ""),
                    "total": videos_res.get("total", 0)
                }
            }

        except DataFetchError as e:
            utils.logger.error(f"[DouyinAPI.get_user_videos] 获取用户作品失败: {e}")
            return {
                "success": False,
                "message": f"获取用户作品失败: {str(e)}",
                "data": None
            }

    async def create_douyin_client(self, httpx_proxy: Optional[str]) -> DOUYINClient:
        """创建抖音客户端"""
        if not self.browser_context or not self.context_page:
            raise Exception("浏览器上下文未初始化")

        cookie_str, cookie_dict = utils.convert_cookies(await self.browser_context.cookies())
        douyin_client = DOUYINClient(
            proxies=httpx_proxy,
            headers={
                "User-Agent": await self.context_page.evaluate("() => navigator.userAgent"),
                "Cookie": cookie_str,
                "Host": "www.douyin.com",
                "Origin": "https://www.douyin.com/",
                "Referer": "https://www.douyin.com/",
                "Content-Type": "application/json;charset=UTF-8"
            },
            playwright_page=self.context_page,
            cookie_dict=cookie_dict,
        )
        return douyin_client

    async def launch_browser(
            self,
            chromium: BrowserType,
            playwright_proxy: Optional[Dict],
            user_agent: Optional[str],
            headless: bool = True
    ) -> BrowserContext:
        """启动浏览器并创建浏览器上下文"""
        if self.use_virtual_browser:
            # 使用指纹浏览器
            utils.logger.info("[DouyinAPI.launch_browser] 使用指纹浏览器")
            return await self._launch_virtual_browser(chromium)
        else:
            # 使用普通浏览器
            utils.logger.info("[DouyinAPI.launch_browser] 使用普通浏览器")
            if config.SAVE_LOGIN_STATE:
                user_data_dir = os.path.join(os.getcwd(), "browser_data",
                                             config.USER_DATA_DIR % "douyin")
                browser_context = await chromium.launch_persistent_context(
                    user_data_dir=user_data_dir,
                    accept_downloads=True,
                    headless=headless,
                    proxy=playwright_proxy,
                    viewport={"width": 1920, "height": 1080},
                    user_agent=user_agent
                )
                return browser_context
            else:
                browser = await chromium.launch(headless=headless, proxy=playwright_proxy)
                browser_context = await browser.new_context(
                    viewport={"width": 1920, "height": 1080},
                    user_agent=user_agent
                )
                return browser_context

    async def _launch_virtual_browser(self, chromium: BrowserType) -> BrowserContext:
        """启动指纹浏览器"""
        try:
            # 初始化指纹浏览器API
            if not self.virtual_browser_api:
                self.virtual_browser_api = VirtualBrowserApi(config.VIRTUAL_BROWSER_API_KEY)
                utils.logger.info(f"[DouyinAPI._launch_virtual_browser] 初始化指纹浏览器API: {config.VIRTUAL_BROWSER_BASE_URL}")

            # 创建虚拟浏览器
            browser_name = f"douyin_browser_{int(asyncio.get_event_loop().time())}"
            self.virtual_browser_id = self.virtual_browser_api.add_browser(
                browser_name=browser_name,
                browser_group=config.VIRTUAL_BROWSER_GROUP,
                os=config.VIRTUAL_BROWSER_OS,
                chrome_version=config.VIRTUAL_BROWSER_CHROME_VERSION
            )

            if self.virtual_browser_id == -1:
                raise Exception("创建虚拟浏览器失败")

            utils.logger.info(f"[DouyinAPI._launch_virtual_browser] 创建虚拟浏览器成功，ID: {self.virtual_browser_id}")

            # 启动虚拟浏览器
            launch_response = self.virtual_browser_api.launch_browser(self.virtual_browser_id)
            if not launch_response.get('success'):
                raise Exception(f"启动虚拟浏览器失败: {launch_response}")

            debugging_port = launch_response['data']['debuggingPort']
            utils.logger.info(f"[DouyinAPI._launch_virtual_browser] 虚拟浏览器调试端口: {debugging_port}")

            # 连接到虚拟浏览器
            browser = await chromium.connect_over_cdp(f"http://localhost:{debugging_port}")

            # 使用现有的浏览器上下文
            if browser.contexts:
                browser_context = browser.contexts[0]
            else:
                browser_context = await browser.new_context()

            utils.logger.info("[DouyinAPI._launch_virtual_browser] 成功连接到指纹浏览器")
            return browser_context

        except Exception as e:
            utils.logger.error(f"[DouyinAPI._launch_virtual_browser] 启动指纹浏览器失败: {e}")
            # 清理资源
            if self.virtual_browser_id and self.virtual_browser_api:
                try:
                    self.virtual_browser_api.stop_browser(self.virtual_browser_id)
                    self.virtual_browser_api.del_browser(self.virtual_browser_id)
                except:
                    pass
                self.virtual_browser_id = None
            raise e

    async def close(self) -> Dict[str, Any]:
        """关闭浏览器上下文"""
        try:
            if self.browser_context:
                await self.browser_context.close()
                self.browser_context = None
                self.context_page = None
                self.dy_client = None

            # 清理指纹浏览器资源
            if self.use_virtual_browser and self.virtual_browser_id and self.virtual_browser_api:
                try:
                    utils.logger.info(f"[DouyinAPI.close] 清理指纹浏览器资源，ID: {self.virtual_browser_id}")
                    self.virtual_browser_api.stop_browser(self.virtual_browser_id)
                    self.virtual_browser_api.del_browser(self.virtual_browser_id)
                    utils.logger.info("[DouyinAPI.close] 指纹浏览器资源清理完成")
                except Exception as e:
                    utils.logger.warning(f"[DouyinAPI.close] 清理指纹浏览器资源失败: {e}")
                finally:
                    self.virtual_browser_id = None
                    self.virtual_browser_api = None

            self.initialized = False
            self.logged_in = False
            self.use_virtual_browser = False

            return {
                "success": True,
                "message": "API已关闭",
                "data": None
            }
        except Exception as e:
            utils.logger.error(f"[DouyinAPI.close] 关闭失败: {e}")
            return {
                "success": False,
                "message": f"关闭失败: {str(e)}",
                "data": None
            }

    def get_status(self) -> Dict[str, Any]:
        """获取API状态"""
        return {
            "success": True,
            "message": "状态获取成功",
            "data": {
                "initialized": self.initialized,
                "logged_in": self.logged_in,
                "has_browser_context": self.browser_context is not None,
                "has_client": self.dy_client is not None,
                "use_virtual_browser": self.use_virtual_browser,
                "virtual_browser_id": self.virtual_browser_id,
                "has_virtual_browser_api": self.virtual_browser_api is not None
            }
        }

    @staticmethod
    def format_proxy_info(ip_proxy_info: IpInfoModel) -> Tuple[Optional[Dict], Optional[Dict]]:
        """格式化代理信息"""
        playwright_proxy = {
            "server": f"{ip_proxy_info.protocol}{ip_proxy_info.ip}:{ip_proxy_info.port}",
            "username": ip_proxy_info.user,
            "password": ip_proxy_info.password,
        }
        httpx_proxy = {
            f"{ip_proxy_info.protocol}": f"http://{ip_proxy_info.user}:{ip_proxy_info.password}@{ip_proxy_info.ip}:{ip_proxy_info.port}"
        }
        return playwright_proxy, httpx_proxy

    def _get_stealth_js_path(self) -> str:
        """获取stealth.min.js文件的绝对路径"""
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
        stealth_js_path = os.path.join(project_root, 'crawlers', 'libs', 'stealth.min.js')
        return stealth_js_path

    def get_virtual_browser_list(self, group_name: str = None) -> Dict[str, Any]:
        """获取指纹浏览器列表"""
        try:
            if not self.virtual_browser_api:
                self.virtual_browser_api = VirtualBrowserApi(config.VIRTUAL_BROWSER_API_KEY)

            group = group_name or config.VIRTUAL_BROWSER_GROUP
            browser_list = self.virtual_browser_api.query_browser_list(group)

            return {
                "success": True,
                "message": f"成功获取 {len(browser_list)} 个浏览器",
                "data": {
                    "browsers": browser_list,
                    "group": group
                }
            }
        except Exception as e:
            utils.logger.error(f"[DouyinAPI.get_virtual_browser_list] 获取浏览器列表失败: {e}")
            return {
                "success": False,
                "message": f"获取浏览器列表失败: {str(e)}",
                "data": None
            }


# 使用示例
async def main():
    """使用示例"""
    api = DouyinAPI()

    try:
        # 查看指纹浏览器列表（可选）
        browser_list_result = api.get_virtual_browser_list()
        print("指纹浏览器列表:", browser_list_result)

        # 初始化API（不需要登录，使用指纹浏览器）
        init_result = await api.initialize(
            need_login=False,
            headless=False,
            enable_proxy=False,
            use_virtual_browser=True  # 启用指纹浏览器
        )
        print("初始化结果:", init_result)

        # 查看API状态
        status = api.get_status()
        print("API状态:", status)

        if init_result["success"]:
            # 搜索视频
            search_result = await api.search_videos(
                keyword="编程",
                count=10,
                sort_type=0,
                publish_time=0
            )
            print("搜索结果:", search_result)

            # 如果有搜索结果，获取第一个视频的详情和评论
            if search_result["success"] and search_result["data"]["videos"]:
                first_video = search_result["data"]["videos"][0]
                aweme_id = first_video["aweme_id"]

                # 获取视频详情
                detail_result = await api.get_video_detail(aweme_id)
                print("视频详情:", detail_result)

                # 获取视频评论
                comments_result = await api.get_video_comments(
                    aweme_id=aweme_id,
                    count=10,
                    get_sub_comments=True
                )
                print("评论结果:", comments_result)

                # 获取作者信息
                author_sec_uid = first_video["author"]["sec_uid"]
                if author_sec_uid:
                    user_info_result = await api.get_user_info(author_sec_uid)
                    print("用户信息:", user_info_result)

                    # 获取用户作品
                    user_videos_result = await api.get_user_videos(
                        sec_user_id=author_sec_uid,
                        count=10
                    )
                    print("用户作品:", user_videos_result)

    finally:
        # 关闭API
        close_result = await api.close()
        print("关闭结果:", close_result)


if __name__ == "__main__":
    asyncio.run(main())
