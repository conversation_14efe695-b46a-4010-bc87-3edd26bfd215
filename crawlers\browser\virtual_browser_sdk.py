import requests
import json
import async<PERSON>
from typing import Dict, List, Optional, Any
from playwright.async_api import async_playwright

class VirtualBrowserSDK:
    """SDK for interacting with VirtualBrowser API"""

    def __init__(self, base_url: str, api_key: str):
        """
        Initialize the VirtualBrowser SDK

        Args:
            base_url: API base URL (e.g., http://localhost:9000)
            api_key: API authentication key
        """
        self.base_url = base_url.rstrip('/')
        self.headers = {
            "Content-Type": "application/json",
            "api-key": api_key
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)

    def _make_request(self, method: str, endpoint: str, data: Optional[Dict] = None) -> Dict:
        """
        Make an HTTP request to the API

        Args:
            method: HTTP method (GET/POST)
            endpoint: API endpoint path
            data: Request payload

        Returns:
            API response dictionary
        """
        url = f"{self.base_url}{endpoint}"
        try:
            response = self.session.request(
                method=method,
                url=url,
                data=json.dumps(data) if data else None
            )
            response.raise_for_status()
            result = response.json()
            if not result.get("success"):
                raise ValueError(f"API request failed: {result.get('message', 'Unknown error')}")
            return result
        except Exception as e:
            raise Exception(f"Request failed: {str(e)}")

    def get_browser_list(self, group: str = "", name: str = "", remark: str = "") -> List[Dict]:
        """
        Get list of browser environments

        Args:
            group: Filter by group
            name: Filter by name
            remark: Filter by remark

        Returns:
            List of browser environment configurations
        """
        data = {}
        if group:
            data["group"] = group
        if name:
            data["name"] = name
        if remark:
            data["remark"] = remark

        response = self._make_request("POST" if data else "GET", "/api/getBrowserList", data)
        return response.get("data", [])

    def get_browser_full_parameters(self, browser_id: Optional[int] = None) -> Dict:
        """
        Get complete parameters for a browser environment

        Args:
            browser_id: Browser environment ID (optional)

        Returns:
            Browser environment parameters
        """
        endpoint = "/api/getBrowserFullParameters"
        if browser_id:
            endpoint += f"?id={browser_id}"
        response = self._make_request("GET", endpoint)
        return response.get("data", {})

    def get_running_browsers(self) -> List[Dict]:
        """
        Get list of running browser instances

        Returns:
            List of running browser instances
        """
        response = self._make_request("GET", "/api/getBrowserRunningList")
        return response.get("data", [])

    def add_browser(self, config: Dict) -> int:
        """
        Create a new browser environment

        Args:
            config: Browser configuration dictionary

        Returns:
            New browser environment ID
        """
        response = self._make_request("POST", "/api/addBrowser", config)
        return response.get("data", {}).get("id")

    def update_browser(self, browser_id: int, config: Dict) -> bool:
        """
        Update a browser environment

        Args:
            browser_id: Browser environment ID
            config: Updated configuration

        Returns:
            Success status
        """
        config["id"] = browser_id
        response = self._make_request("POST", "/api/updateBrowser", config)
        return response.get("success", False)

    def delete_browser(self, browser_id: int) -> bool:
        """
        Delete a browser environment

        Args:
            browser_id: Browser environment ID

        Returns:
            Success status
        """
        response = self._make_request("POST", "/api/deleteBrowser", {"id": browser_id})
        return response.get("success", False)

    async def launch_browser(self, browser_id: int, name: Optional[str] = None, remark: Optional[str] = None) -> Dict:
        """
        Launch a browser environment

        Args:
            browser_id: Browser environment ID
            name: Optional browser name
            remark: Optional browser remark

        Returns:
            Launch response including debugging port
        """
        data = {"id": browser_id}
        if name:
            data["name"] = name
        if remark:
            data["remark"] = remark
        response = self._make_request("POST", "/api/launchBrowser", data)
        return response

    async def stop_browser(self, browser_id: int) -> bool:
        """
        Stop a browser environment

        Args:
            browser_id: Browser environment ID

        Returns:
            Success status
        """
        response = self._make_request("POST", "/api/stopBrowser", {"id": browser_id})
        return response.get("success", False)

    def get_group_list(self) -> List[Dict]:
        """
        Get list of groups

        Returns:
            List of group configurations
        """
        response = self._make_request("GET", "/api/getGroupList")
        return response.get("data", [])

    def add_group(self, name: str) -> bool:
        """
        Create a new group

        Args:
            name: Group name

        Returns:
            Success status
        """
        response = self._make_request("POST", "/api/addGroup", {"name": name})
        return response.get("success", False)

    def update_group(self, group_id: int, name: str) -> bool:
        """
        Update a group

        Args:
            group_id: Group ID
            name: New group name

        Returns:
            Success status
        """
        response = self._make_request("POST", "/api/updateGroup", {"groupId": group_id, "name": name})
        return response.get("success", False)

    def delete_group(self, group_id: int) -> bool:
        """
        Delete a group

        Args:
            group_id: Group ID

        Returns:
            Success status
        """
        response = self._make_request("POST", "/api/deleteGroup", {"id": group_id})
        return response.get("success", False)

    def get_crx_list(self) -> List[Dict]:
        """
        Get list of plugins

        Returns:
            List of plugin configurations
        """
        response = self._make_request("GET", "/api/getCrxList")
        return response.get("data", [])

    def add_crx(self, store_url: str) -> bool:
        """
        Add a plugin

        Args:
            store_url: Plugin store URL

        Returns:
            Success status
        """
        response = self._make_request("POST", "/api/addCrx", {"storeUrl": store_url})
        return response.get("success", False)

    def delete_crx(self, crx_id: str) -> bool:
        """
        Delete a plugin

        Args:
            crx_id: Plugin ID

        Returns:
            Success status
        """
        response = self._make_request("POST", "/api/deleteCrx", {"id": crx_id})
        return response.get("success", False)

    async def automate_browser(self, browser_id: int, url: str, name: Optional[str] = None, remark: Optional[str] = None) -> None:
        """
        Automate browser operations using Playwright

        Args:
            browser_id: Browser environment ID
            url: URL to navigate to
            name: Optional browser name
            remark: Optional browser remark
        """
        launch_response = await self.launch_browser(browser_id, name, remark)
        if not launch_response.get("success"):
            raise ValueError("Failed to launch browser")

        debugging_port = launch_response.get("data").get("debuggingPort")
        if not debugging_port:
            raise ValueError("No debugging port provided")

        async with async_playwright() as p:
            browser = await p.chromium.connect_over_cdp(f"http://localhost:{debugging_port}")
            try:
                contexts = browser.contexts
                if contexts:
                    context = contexts[0]
                else:
                    context = await browser.new_context()

                page = await context.new_page()
                await page.goto(url)

                # Add your automation logic here

                await self.stop_browser(browser_id)
            finally:
                await browser.close()

if __name__ == "__main__":
    async def example_usage():
        sdk = VirtualBrowserSDK("http://localhost:9000", "eRYQnY16tPOLq1vQjbKusOImowrvyCk8")

        # Example: Create a browser environment
        browser_config = {
            "name": "TestBrowser",
            "group": "TestGroup",
            "os": "Win 11",
            "chrome_version": 126,
            "homepage": {"mode": 1, "value": "https://www.example.com"}
        }
        browser_id = sdk.add_browser(browser_config)
        print(f"Created browser with ID: {browser_id}")

        # Example: Launch and automate browser
        await sdk.automate_browser(browser_id, "https://www.doubao.com/chat/", name="TestBrowser", remark="Test automation")

        # Example: Get browser list
        browsers = sdk.get_browser_list()
        print("Browser list:", browsers)

        # Example: Delete browser
        sdk.delete_browser(browser_id)

    asyncio.run(example_usage())