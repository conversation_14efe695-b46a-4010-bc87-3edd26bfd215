"""
This module contains platform search functionality for XHS and <PERSON>uy<PERSON>.
"""

import asyncio
import os
from datetime import datetime
from typing import List, Dict, Any, Optional

from crawlers.account_manager import AccountManager
from crawlers.spiders.douyin.douyin_search import DouyinSearch
from crawlers.spiders.xhs import SearchSortType, SearchNoteType
from crawlers.spiders.xhs.xhs_search import XiaoHongShuSearch
from myScrapy.api.service_api import ServiceApi
from tools import utils

# 全局变量，用于存储搜索实例
xhs_searcher: XiaoHongShuSearch = None
douyin_searcher: DouyinSearch = None

# 搜索锁，确保同一时间只执行一个搜索任务
xhs_search_lock = asyncio.Lock()
douyin_search_lock = asyncio.Lock()


async def initialize_xhs_searcher(
        chrome_path: str, user_data_dir: Optional[str] = None
) -> XiaoHongShuSearch:
    """初始化小红书搜索实例"""
    global xhs_searcher
    if xhs_searcher is None or not xhs_searcher.initialized:
        utils.logger.info("[initialize_xhs_searcher] 正在初始化小红书搜索实例...")
        xhs_searcher = XiaoHongShuSearch(
            headless=False,
            use_local_chrome=True,
            chrome_path=chrome_path,
            user_data_dir=user_data_dir,
        )
        await xhs_searcher.initialize()
        utils.logger.info("[initialize_xhs_searcher] 小红书搜索实例初始化完成")
    return xhs_searcher


async def initialize_douyin_searcher(
        chrome_path: str, user_data_dir: Optional[str] = None
) -> DouyinSearch:
    """初始化抖音搜索实例"""
    global douyin_searcher
    if douyin_searcher is None or not douyin_searcher.initialized:
        utils.logger.info("[initialize_douyin_searcher] 正在初始化抖音搜索实例...")
        douyin_searcher = DouyinSearch(
            headless=False,
            use_local_chrome=True,
            chrome_path=chrome_path,
            user_data_dir=user_data_dir,
        )
        await douyin_searcher.initialize()
        utils.logger.info("[initialize_douyin_searcher] 抖音搜索实例初始化完成")
    return douyin_searcher


async def execute_xhs_search(task_info: Dict[str, Any]) -> dict | list[Any]:
    """执行小红书搜索"""
    async with xhs_search_lock:
        try:
            keyword = task_info.get("keyword")
            limit = task_info.get("limit", 200)  # 默认获取100条结果

            # 执行搜索
            search_results = await xhs_searcher.search(
                keyword=keyword,
                limit=limit,
                sort_type=SearchSortType.GENERAL,
                note_type=SearchNoteType.ALL,
            )

            utils.logger.info(
                f"[execute_xhs_search] 小红书搜索完成，获取到 {len(search_results)} 条结果"
            )
            return search_results
        except Exception as e:
            utils.logger.error(f"[execute_xhs_search] 小红书搜索过程中出现错误: {e}")
            import traceback

            traceback.print_exc()
            return []


async def execute_douyin_search(task_info: Dict[str, Any]) -> List:
    """执行抖音搜索"""
    async with douyin_search_lock:

        try:
            keyword = task_info.get("keyword")
            limit = task_info.get("limit", 200)  # 默认获取100条结果

            utils.logger.info(f"[execute_douyin_search] 开始搜索抖音关键词: {keyword}")

            # 执行搜索
            search_results = await douyin_searcher.search(
                keyword=keyword, limit=120
            )

            utils.logger.info(
                f"[execute_douyin_search] 抖音搜索完成，获取到 {len(search_results)} 条结果"
            )
            return search_results
        except Exception as e:
            utils.logger.error(f"[execute_douyin_search] 抖音搜索过程中出现错误: {e}")
            import traceback

            traceback.print_exc()
            return []


def _build_xhs_temp_data(task_info: dict, data: List) -> list:
    """
    {'id': '681d91bd00000000210063e7',
     'model_type': 'note',
     'note_card':
        {
        'type': 'normal',
        'display_title': '5⃣️🈷️分享好物兑换2⃣️',
         'user':
            {
                'nick_name': '满意仔 🐑',
                'avatar': 'https://sns-avatar-qc.xhscdn.com/avatar/1040g2jo3101ic35k5u0048ts53n0q4dn21n6cto?imageView2/2/w/80/format/jpg',
                'user_id': '583bee0d7fc5b8736ea711b7',
                'nickname': '满意仔 🐑',
                 'xsec_token': 'ABbk-qBJA2OCx-o9vuNiuATHMDv1T5_YjydqhLIZUvTXw='
            },
        'interact_info':
            {
                'liked_count': '8',
                'collected': False,
                 'collected_count': '4',
                  'comment_count': '0',
                  'shared_count': '1',
                  'liked': False
            },
             'cover':
             'corner_tag_info': [{'type': 'publish_time', 'text': '34分钟前'}]
            },
                'xsec_token': 'ABq-6fVmfEHkUqPfS6DzMRBQkDg3NVApMthgER9BO6P-0='}
    Args:
        task_info:
        data:

    Returns:

    """

    task_id = task_info.get("taskId")
    kw_uuid = task_info.get("kwUuid")

    result_list = []

    for item in data:
        try:
            tem_data = {
                "taskId": task_id,
                "kwUuid": kw_uuid,
                "newsUrl": "https://www.xiaohongshu.com/discovery/item/" + item["id"],
                "newsLongUrl": "https://www.xiaohongshu.com/discovery/item/" + item["id"],
                "platformName": "小红书",
                "platformType": "xhs",
                "mediaIdentity": item["note_card"]["user"]["user_id"],
                "mediaId": item["note_card"]["user"]["user_id"],
                "mediaName": item["note_card"]["user"]["nick_name"],
                "newsLikeCount": item["note_card"]["interact_info"]["liked_count"],
                "newsCommentCount": item["note_card"]["interact_info"]["comment_count"],
                "newsCollectCnt": item["note_card"]["interact_info"]["collected_count"],
                "newsRepostsCount": item["note_card"]["interact_info"]["shared_count"],
                "mediaPicurl": item["note_card"]["user"]["avatar"],
                "status": 0,
            }
            result_list.append(tem_data)
        except Exception as e:
            # 可能有关广告或者推荐数据
            utils.logger.info(f"[error format xhs] item :{item}")
            utils.logger.info(f"[error exception xhs] item :{e}")
            continue

    return result_list


def _build_dy_temp_data(task_info: dict, data: List) -> list:
    task_id = task_info.get("taskId")
    kw_uuid = task_info.get("kwUuid")

    result_list = []
    for item in data:

        try:
            video = item.get("aweme_info")
            music = video.get("music", "")
            stats = video.get("statistics", "")
            author = video.get("author", "")

            tem_data = {
                "taskId": task_id,
                "kwUuid": kw_uuid,
                "newsUrl": "https://www.douyin.com/video/" + video["aweme_id"],
                "newsLongUrl": "https://www.douyin.com/video/" + video["aweme_id"],
                "platformName": "抖音",
                "platformType": "dy",
                "mediaIdentity": "https://www.douyin.com/user/" + author["sec_uid"],
                "mediaId": author["uid"],
                "mediaName": author["nickname"],
                "newsTitle": video.get("desc", ""),
                "newsDigest": video.get("desc", ""),
                "newsPosttime": datetime.fromtimestamp(video["create_time"]).strftime(
                    "%Y-%m-%d %H:%M:%S"
                ),
                # "musicUrl": video["music"]["music_url"],
                # "musicAuthorName": video["music"]["author"],
                # "musicName": video["music"]["author"],
                # "musicId": video["music"]["id_str"],
                "newsContent": video["desc"],
                "newsLikeCount": stats["digg_count"],
                "newsCommentCount": stats["comment_count"],
                "newsCollectCnt": stats["collect_count"],
                "newsRepostsCount": stats["share_count"],
                "newsHeadimgUrl": video["video"]["cover"]["url_list"][0],
                "mediaPicurl": author["avatar_thumb"]["url_list"][0],
                "secUid": author["sec_uid"],
                "status": 1,
            }
            result_list.append(tem_data)
        except Exception as e:
            utils.logger.error(f"[formatter error] 处理任务失败: {e}")
            continue
    return result_list


async def process_task(task_info: Dict[str, Any]) -> bool:
    """处理任务"""
    try:
        platform = task_info.get("platform")
        keyword = task_info.get("keyword")

        utils.logger.info(
            f"[process_task] 开始处理任务: 平台={platform}, 关键词={keyword}"
        )

        # 根据平台类型执行不同的搜索
        if platform == "xhs":
            search_results = await execute_xhs_search(task_info)
            if not search_results:
                utils.logger.warning(f"[process_task] 小红书搜索未返回结果")
                return False

            formatted_results = _build_xhs_temp_data(task_info, search_results)

        elif platform == "dy":
            search_results = await execute_douyin_search(task_info)
            if not search_results:
                utils.logger.warning(f"[process_task] 抖音搜索未返回结果")
                return False

            formatted_results = _build_dy_temp_data(task_info, search_results)

        else:
            utils.logger.error(f"[process_task] 不支持的平台类型: {platform}")
            return False

        # 将结果发送回服务器
        api = ServiceApi()
        success = api.update_task_result(task_info, formatted_results)

        if success:
            utils.logger.info(
                f"[process_task] 任务处理成功: 平台={platform}, 关键词={keyword}, 结果数量={len(formatted_results)}"
            )
        else:
            utils.logger.error(
                f"[process_task] 任务结果上传失败: 平台={platform}, 关键词={keyword}"
            )

        return success
    except Exception as e:
        utils.logger.error(f"[process_task] 处理任务过程中出现错误: {e}")
        import traceback

        traceback.print_exc()
        return False


async def main_loop(
        chrome_path: str = None,
        xhs_user_data_dir: str = None,
        douyin_user_data_dir: str = None,
):
    # 配置浏览器路径
    if not chrome_path:
        chrome_path = (
            r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe"
        )

    # 配置用户数据目录
    if not xhs_user_data_dir:
        xhs_user_data_dir = os.path.join(os.getcwd(), "browser_data", "xhs_data")
    if not douyin_user_data_dir:
        douyin_user_data_dir = os.path.join(os.getcwd(), "browser_data", "douyin_data")

    utils.logger.info(f"[main_loop] Chrome路径: {chrome_path}")
    utils.logger.info(f"[main_loop] 小红书用户数据目录: {xhs_user_data_dir}")
    utils.logger.info(f"[main_loop] 抖音用户数据目录: {douyin_user_data_dir}")

    # 初始化搜索实例
    await initialize_xhs_searcher(chrome_path, xhs_user_data_dir)
    await initialize_douyin_searcher(chrome_path, douyin_user_data_dir)

    api = ServiceApi()

    try:
        utils.logger.info("[main_loop] 开始监听任务...")

        while True:
            try:
                # 查询任务
                task_info = api.query_task_ack()

                if not task_info:
                    await asyncio.sleep(2)
                    continue

                # 处理任务
                await process_task(task_info)

            except Exception as e:
                utils.logger.error(f"[main_loop] 任务处理过程中出现错误: {e}")
                import traceback

                traceback.print_exc()
                await asyncio.sleep(5)  # 出错后稍微休眠一段时间
    finally:
        # 关闭浏览器
        if xhs_searcher and xhs_searcher.initialized:
            await xhs_searcher.close()
            utils.logger.info("[main_loop] 小红书浏览器已关闭")

        if douyin_searcher and douyin_searcher.initialized:
            await douyin_searcher.close()
            utils.logger.info("[main_loop] 抖音浏览器已关闭")


async def test_account_manager():
    """测试账号管理器"""
    acc = AccountManager()
    await acc.start_login(platform="xhs", username="玉峰", user_data=r"C:\user_account\yufeng")


async def test_user_data():
    """测试用户数据"""
    # searcher = XiaoHongShuSearch(
    #             headless=False,
    #             use_local_chrome=True,
    #             user_data_dir=r"F:\account\xhs\kk",
    #             chrome_path=r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe")
    await initialize_xhs_searcher(chrome_path=r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe",
                                  user_data_dir=r"E:\user_data\xhs_data")
    try:

        user_info_list = await xhs_searcher.query_user_data_list(user_id_list=["5ffd51530000000001009c38"])
        print(user_info_list)
    except Exception as e:
        utils.logger.error(f"main: {e}")

    finally:
        # 关闭浏览器
        await xhs_searcher.close()

async def test_user_info():
    """测试用户数据"""
    await initialize_xhs_searcher(chrome_path=r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe",
                                  user_data_dir=r"E:\user_data\xhs_data")
    try:
        user_info = await xhs_searcher.search(limit=20, keyword="grok怎么样")
        task_info = {
            "task_info": "122",
            "kwUuid": "123333"
        }
        formatted_results = _build_xhs_temp_data(task_info, user_info)
        print(formatted_results)
    except Exception as e:
        utils.logger.error(f"main: {e}")

    finally:
        # 关闭浏览器
        await xhs_searcher.close()


async def test_note_detail(note_id: str = ""):
    """测试用户数据"""
    await initialize_xhs_searcher(chrome_path=r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe",
                                  user_data_dir=r"E:\user_data\xhs_data")
    try:
        user_info = await xhs_searcher.get_note_details()
        formatted_results = _build_xhs_temp_data(task_info, user_info)
        print(formatted_results)
    except Exception as e:
        utils.logger.error(f"main: {e}")

    finally:
        # 关闭浏览器
        await xhs_searcher.close()

async def test_dy_user_data():
    """测试用户数据"""
    await initialize_douyin_searcher(chrome_path=r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe",
                                     user_data_dir=r"E:\user_data\douyin_data")
    try:

        user_info_list = await douyin_searcher.get_creators_today_videos(
            user_list=["MS4wLjABAAAA--_fkDAvBMz1bOP-aU5rwoDp4gp4GVsidxPeq6li4-M"])

        print(user_info_list)
    except Exception as e:
        utils.logger.error(f"main: {e}")

    finally:
        # 关闭浏览器
        await xhs_searcher.close()


if __name__ == "__main__":
    # 运行主循环
    asyncio.run(
        main_loop(
            chrome_path=r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe",
            xhs_user_data_dir=r"E:\user_data\xhs_data",
            douyin_user_data_dir=r"E:\user_data\douyin_data",
        )
    )
    # asyncio.run(test_dy_user_data())
