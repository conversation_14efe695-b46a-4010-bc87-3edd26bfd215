# 抖音API指纹浏览器集成使用说明

## 概述

本文档介绍如何在抖音爬虫API中使用指纹浏览器功能。指纹浏览器可以提供更好的反检测能力，模拟真实用户的浏览器环境。

## 配置

### 1. 基础配置

在 `config/base_config.py` 中添加了以下配置项：

```python
# 指纹浏览器配置
ENABLE_VIRTUAL_BROWSER = False  # 是否启用指纹浏览器
VIRTUAL_BROWSER_API_KEY = "your_api_key_here"  # 指纹浏览器API密钥
VIRTUAL_BROWSER_BASE_URL = "http://localhost:9000"  # 指纹浏览器API地址
VIRTUAL_BROWSER_GROUP = "默认分组"  # 指纹浏览器分组名称
VIRTUAL_BROWSER_OS = "Win 11"  # 指纹浏览器操作系统
VIRTUAL_BROWSER_CHROME_VERSION = 132  # 指纹浏览器Chrome版本
```

### 2. 启用指纹浏览器

有两种方式启用指纹浏览器：

#### 方式1：全局配置
在 `config/base_config.py` 中设置：
```python
ENABLE_VIRTUAL_BROWSER = True
```

#### 方式2：运行时指定
在初始化API时指定：
```python
await api.initialize(use_virtual_browser=True)
```

## 使用方法

### 基本使用

```python
import asyncio
from crawlers.spiders.douyin.douyin_api import DouyinAPI

async def main():
    api = DouyinAPI()
    
    try:
        # 初始化API，启用指纹浏览器
        result = await api.initialize(
            need_login=False,
            headless=True,
            enable_proxy=False,
            use_virtual_browser=True  # 启用指纹浏览器
        )
        
        if result["success"]:
            print("初始化成功，使用指纹浏览器")
            
            # 查看API状态
            status = api.get_status()
            print(f"指纹浏览器ID: {status['data']['virtual_browser_id']}")
            
            # 进行正常的爬虫操作
            search_result = await api.search_videos("编程", count=10)
            print(f"搜索到 {len(search_result['data']['videos'])} 个视频")
            
    finally:
        # 关闭API，自动清理指纹浏览器资源
        await api.close()

if __name__ == "__main__":
    asyncio.run(main())
```

### 高级功能

#### 1. 查看指纹浏览器列表

```python
api = DouyinAPI()

# 查看默认分组的浏览器列表
browser_list = api.get_virtual_browser_list()
print(browser_list)

# 查看指定分组的浏览器列表
browser_list = api.get_virtual_browser_list("我的分组")
print(browser_list)
```

#### 2. 检查API状态

```python
status = api.get_status()
print(f"是否使用指纹浏览器: {status['data']['use_virtual_browser']}")
print(f"指纹浏览器ID: {status['data']['virtual_browser_id']}")
print(f"是否有指纹浏览器API: {status['data']['has_virtual_browser_api']}")
```

## API参考

### DouyinAPI.initialize()

```python
async def initialize(self,
                     need_login: bool = False,
                     login_type: str = "qrcode",
                     login_phone: str = "",
                     cookie_str: str = "",
                     headless: bool = True,
                     enable_proxy: bool = False,
                     use_virtual_browser: bool = None) -> Dict[str, Any]:
```

**参数说明：**
- `use_virtual_browser`: 是否使用指纹浏览器，None时使用配置文件设置

### DouyinAPI.get_virtual_browser_list()

```python
def get_virtual_browser_list(self, group_name: str = None) -> Dict[str, Any]:
```

**参数说明：**
- `group_name`: 浏览器分组名称，None时使用配置文件设置

### DouyinAPI.get_status()

返回API状态，包括指纹浏览器相关信息：
- `use_virtual_browser`: 是否使用指纹浏览器
- `virtual_browser_id`: 当前指纹浏览器ID
- `has_virtual_browser_api`: 是否有指纹浏览器API实例

## 注意事项

1. **资源清理**：使用完毕后务必调用 `api.close()` 方法，确保指纹浏览器资源被正确清理。

2. **错误处理**：如果指纹浏览器启动失败，系统会自动清理资源并抛出异常。

3. **配置检查**：确保指纹浏览器服务正在运行，且API密钥正确。

4. **性能考虑**：指纹浏览器启动需要一定时间，建议在需要长时间运行的场景中使用。

## 故障排除

### 常见问题

1. **连接失败**
   - 检查指纹浏览器服务是否运行
   - 检查API地址和端口是否正确
   - 检查API密钥是否有效

2. **浏览器创建失败**
   - 检查分组名称是否存在
   - 检查账户配额是否充足
   - 检查操作系统和Chrome版本配置

3. **资源清理问题**
   - 确保调用了 `api.close()` 方法
   - 检查日志中的清理信息

### 调试模式

启用详细日志以获取更多调试信息：

```python
import logging
logging.basicConfig(level=logging.INFO)
```

## 示例代码

完整的示例代码请参考：
- `test_virtual_browser_integration.py` - 集成测试脚本
- `crawlers/spiders/douyin/douyin_api.py` 中的 `main()` 函数
