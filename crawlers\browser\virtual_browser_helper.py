# -*- coding: utf-8 -*- 
# @Time : 2025/5/28 17:00 
# <AUTHOR> cyf
# @File : virtual_browser_helper.py
from typing import List, Optional

from common.crawler_platform import CrawlerPlatform
from virtual_browser_sdk import VirtualBrowserSDK


class VirtualBrowserHelper:

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.browser_api = VirtualBrowserSDK("http://localhost:9000", api_key)

    def set_up_group(self, group_name_list: List[CrawlerPlatform]) -> bool:
        """
        初始化各爬虫分组分组，第一次启动 浏览器分组
        """

        pass

    async def apply_browser(self, launch_new: bool, crawler_group: CrawlerPlatform) -> Optional[str]:
        """
        launch_new = Ture 则直接在分组下添加并启动一个新的浏览器，

        launch_new = False 如果该平台分组下有未启动的浏览器，则选择一个启动，
        如果没有则增加一个浏览器实例， 同时启动，返回 debuggerPort
        :param launch_new:
        :param crawler_group:
        :return:
        """

        pass

    def _cover_name(self, crawler_group: CrawlerPlatform):
        """
        将平台改为分组名称
        :param crawler_group:
        :return:
        """
        if crawler_group:
            return crawler_group.value
        else:
            return "默认分组"


if __name__ == '__main__':
    browser_helper: VirtualBrowserHelper = VirtualBrowserHelper(api_key="")

    platform_list: List[CrawlerPlatform] = [CrawlerPlatform.K_S, CrawlerPlatform.X_H_S, CrawlerPlatform.D_Y,
                                            CrawlerPlatform.W_B]

    browser_helper.set_up_group(group_name_list=platform_list)
